class CreateCalendarEventBenefits < ActiveRecord::Migration[7.2]
  def change
    create_table :calendar_event_benefits do |t|
      t.references :calendar_event, null: false, foreign_key: true
      t.date :event_date, null: false
      t.integer :discount_percentage, default: 0
      t.boolean :available, default: true
      t.integer :voucher_bucket_id
      t.integer :redeemed_rule
      t.integer :redeemed_limit_per_user

      t.timestamps
    end
  end
end
