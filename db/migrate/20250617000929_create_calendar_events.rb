class CreateCalendarEvents < ActiveRecord::Migration[7.2]
  def change
    create_table :calendar_events do |t|
      t.string :title
      t.text :description
      t.references :business, null: false, foreign_key: true
      t.references :calendar_event_promoter, foreign_key: true, null: true
      t.datetime :dates, array: true, default: []
      t.datetime :publish_at
      t.boolean :active, default: true
      t.string :banner_url
      t.integer :max_discount, default: 0
      t.decimal :latitude, precision: 10, scale: 6
      t.decimal :longitude, precision: 10, scale: 6
      t.string :full_address
      t.string :address_street
      t.string :address_number
      t.string :address_city
      t.string :address_state
      t.string :address_country
      t.text :event_info
      t.text :benefit_info
      t.integer :redeemed_amount, default: 0
      t.datetime :deactivated_at
      t.tsvector :search_vector,
        as: "setweight(to_tsvector('portuguese', coalesce(title, '')), 'A') || setweight(to_tsvector('portuguese', coalesce(description, '')), 'B')",
        stored: true

      t.timestamps
    end

    add_index :calendar_events, :dates, using: :gin
    add_index :calendar_events, :search_vector, using: :gin
  end
end
