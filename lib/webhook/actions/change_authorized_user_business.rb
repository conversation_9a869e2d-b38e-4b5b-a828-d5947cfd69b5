# frozen_string_literal: true

class Webhook::Actions::ChangeAuthorizedUserBusiness
  def initialize(authorized_user:, business:, authorized_user_group: nil)
    @business = business
    @authorized_user = authorized_user
    @authorized_user_group = authorized_user_group
    @processed = false
    @success = false
    @message = nil
  end

  def process
    return if processed
    update_authorized_user_business

    @processed = true
  end

  def success?
    process
    @success
  end

  def message
    process
    @status_message
  end

  private

  attr_reader :business, :authorized_user, :authorized_user_group, :processed, :success, :status_message

  def update_authorized_user_business
    if authorized_user.update(business:, authorized_user_group:)
      @success = true
    else
      @success = false
      @status_message = "Não foi possível mudar o usuário de Projeto: #{authorized_user.errors.full_messages.join(", ")}"
    end
  end
end
