# frozen_string_literal: true

class Webhook::Betalabs::Events::ActiveSubscription < Webhook::Betalabs::Events::Base
  def action
    @authorized_user = authorized_user_from_subscription_data

    if authorized_user.present?
      Webhook::Actions::ChangeAuthorizedUserBusiness.new(authorized_user:, business: target_business, authorized_user_group: group_on_target_business)
    else
      Webhook::Actions::CreateAuthorizedUser.new(business: target_business, name:, cpf:, email:, phone:)
    end
  end

  private

  attr_reader :authorized_user

  def cpf = subscription.dig("data", "consumer", "document1")

  def name = subscription.dig("data", "consumer", "full_name")

  def email = subscription.dig("data", "consumer", "email")

  def phone = subscription.dig("data", "consumer", "telephone", "ddd") + subscription.dig("data", "consumer", "telephone", "number")

  def authorized_user_from_subscription_data
    cpf = subscription.dig("data", "consumer", "document1")

    AuthorizedUser.find_by(cpf: cpf, business_id: business.all_project_ids)
  end

  def target_business
    @target_business ||=
      plan_business? ? Business.find(business.betalabs_client.plan_to_businesses_ids[subscription.dig("data", "plan_id").to_s]) : business
  end

  def plan_business?
    business.betalabs_client.plan_to_businesses_ids.present? &&
      business.betalabs_client.plan_to_businesses_ids[subscription.dig("data", "plan_id").to_s].present?
  end

  def group_on_target_business
    target_authorized_user_group = target_business.authorized_user_groups.find_or_initialize_by(slug: authorized_user.authorized_user_group.slug)
    target_authorized_user_group.update(name: authorized_user.authorized_user_group.name) if target_authorized_user_group.new_record?
    target_authorized_user_group
  end
end
