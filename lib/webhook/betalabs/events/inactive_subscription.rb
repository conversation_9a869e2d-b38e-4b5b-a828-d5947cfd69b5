# frozen_string_literal: true

class Webhook::Betalabs::Events::InactiveSubscription < Webhook::Betalabs::Events::Base
  def action
    @authorized_user = authorized_user_from_subscription_data

    return if authorized_user.blank?

    if permission_on_free_plan_business?
      Webhook::Actions::ChangeAuthorizedUserBusiness.new(authorized_user:, business: free_plan_business, authorized_user_group: group_on_free_plan_business)
    else
      Webhook::Actions::InactivateAuthorizedUser.new(cpf: authorized_user.cpf, business: authorized_user.business)
    end
  end

  private

  attr_reader :authorized_user

  def authorized_user_from_subscription_data
    cpf = subscription.dig("data", "consumer", "document1")

    AuthorizedUser.find_by(cpf: cpf, business_id: business.all_project_ids)
  end

  def permission_on_free_plan_business?
    business.betalabs_client.free_plan_business_id.present? && @authorized_user.authorized_user_group.present?
  end

  def free_plan_business
    @free_plan_business ||=
      Business.find(business.betalabs_client.free_plan_business_id)
  end

  def group_on_free_plan_business
    target_authorized_user_group = free_plan_business.authorized_user_groups.find_or_initialize_by(slug: authorized_user.authorized_user_group.slug)
    target_authorized_user_group.update(name: authorized_user.authorized_user_group.name) if target_authorized_user_group.new_record?
    target_authorized_user_group
  end
end
