# frozen_string_literal: true

class Webhook::Betalabs::<PERSON><PERSON><PERSON><PERSON>
  def parse(request:, business:)
    @params = request.params
    @business = business
    log_request(params)

    return Webhook::Actions::BlankAction.new(success: false, message: "Couldn't fetch subscription data") if subscription_response.empty?

    if subscription_response.dig("data", "status") == "active"
      Webhook::Betalabs::Events::ActiveSubscription.new(business:, params:, subscription: subscription_response).action
    elsif !subscription_response.dig("data", "status").in?(["active", "processing"])
      Webhook::Betalabs::Events::InactiveSubscription.new(business:, params:, subscription: subscription_response).action
    else
      Webhook::Actions::BlankAction.new
    end
  end

  def need_authorization? = false

  def need_business? = true

  private

  attr_reader :business, :params

  def log_request(params)
    return if Rails.env.test?

    log_file_path = Rails.root.join("log", "betalabs_event_#{Rails.env}.log")
    Logger.new(log_file_path).error(params.inspect)
  end

  def subscription_response
    @subscription_response ||= begin
      betalab_api = Betalabs::Api.new(betalabs_client: business.betalabs_client)
      subscription_response = betalab_api.subscription(subscription_id: params[:id])
      subscription_response.success? ? JSON.parse(subscription_response.body) : {}
    end
  end
end
