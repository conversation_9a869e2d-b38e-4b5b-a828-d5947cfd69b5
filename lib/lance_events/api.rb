# frozen_string_literal: true

module LanceEvents
  class Api
    WEBHOOK_URL = "https://n8n.lancecorp.com.br/webhook/9aa645d9-7a15-4fb3-8885-e1b9e369f75a"
    TOKEN = "YWxsb3lhbDpMQG5jM0BAMjAyNQ=="

    def self.send_event(params)
      @http_client = build_http_client
      response = @http_client.post("", params)

      if response.success?
        {
          success: true,
          status: response.status,
          body: response.body
        }
      else
        {
          success: false,
          status: response.status,
          body: response.body,
          error: "Request failed with status #{response.status}"
        }
      end
    rescue HttpClient::ConnectionFailed => e
      {
        success: false,
        error: "Connection failed: #{e.message}"
      }
    rescue HttpClient::TimeoutError => e
      {
        success: false,
        error: "Request timeout: #{e.message}"
      }
    rescue HttpClient::ResponseError => e
      {
        success: false,
        status: e.code,
        body: e.body,
        error: "HTTP error #{e.code}: #{e.body}"
      }
    rescue HttpClient::Error => e
      {
        success: false,
        error: "HTTP client error: #{e.message}"
      }
    end

    def self.build_http_client
      HttpClient::HttpClient.new(url: WEBHOOK_URL) do |builder|
        builder.headers["Authorization"] = "Basic #{TOKEN}"
        builder.request :json
        builder.response :json
        builder.adapter :net_http
      end
    end

    private_class_method :build_http_client
  end
end
