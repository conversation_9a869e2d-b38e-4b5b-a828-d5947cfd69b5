# frozen_string_literal: true

class DataGenerator::CalendarEvent
  def self.process(business)
    raise if Rails.env.production?

    (0..10).each do |n|
      promoter = CalendarEventPromoter.create!(
        name: "Promotor do evento #{n}",
        description: "Descrição do promotor"
      )

      CalendarEvent.create!(
        title: "Evento título teste #{n}",
        description: "Evento descrição teste #{n}",
        max_discount: 10,
        dates: [Time.zone.now, Time.zone.now + n.day, Time.zone.now + (n * n).day],
        active: true,
        business_id: business.id,
        calendar_event_promoter_id: promoter.id,
        tags: []
      )
    end

    business
  end
end
