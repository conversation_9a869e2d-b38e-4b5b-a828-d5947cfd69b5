# frozen_string_literal: true

class Order::RewardRedeemLimitValidator < ActiveModel::Validator
  def validate(record)
    redeem_rule = record.reward.redeem_rules.find_by(redeem_rule: RedeemRule::Kind::REDEEM_LIMIT)
    return unless redeem_rule

    orders_count = record.reward.orders.where(status: Order::Status::COMPLETED, user: record.user).count
    if orders_count >= redeem_rule.value.to_i
      record.errors.add(:base, I18n.t("orders.errors.reward_redeem_limit"))
    end
  end
end
