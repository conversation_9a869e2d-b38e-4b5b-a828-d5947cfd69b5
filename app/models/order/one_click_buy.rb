# frozen_string_literal: true

require "securerandom"

module Order::OneClickBuy
  extend ActiveSupport::Concern

  class_methods do
    def one_click_buy(user:, product:)
      ActiveRecord::Base.transaction do
        voucher = product.voucher_bucket.issue(resource: product)
        @order = Order.create!(user:, reward: product, voucher:)
      end

      idempotency_key = SecureRandom.uuid
      Payment::Processor.new(order: @order, idempotency_key:, payment_method: "wallet_withdrawal").call
    end
  end
end
