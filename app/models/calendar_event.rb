class CalendarEvent < ApplicationRecord
  ORDER_MAPPINGS = {
    "popularity" => "redeemed_amount DESC",
    "max_discount" => "max_discount DESC",
    "min_discount" => "max_discount ASC",
    "date" => "dates[1] ASC"
  }.freeze

  belongs_to :business
  belongs_to :calendar_event_promoter, optional: true
  has_many :event_benefits, dependent: :destroy, class_name: "CalendarEventBenefit"
  has_many :orders, through: :event_benefits

  acts_as_taggable_on :tags

  validates :title, presence: true
  validates :description, presence: true
  validates :max_discount, numericality: {
    only_integer: true,
    greater_than_or_equal_to: 0,
    less_than_or_equal_to: 100
  }

  scope :active, -> { where("active IS TRUE") }
  scope :start_after, ->(start_date) { where(start_datetime: start_date..) }
  scope :end_before, ->(end_date) { where(end_datetime: ..end_date) }
  scope :by_popularity, -> { order(redeemed_amount: :desc) }
  scope :start_after, lambda { |start_date|
    where("array_length(dates, 1) > 0 AND dates[1] >= ?", start_date.to_date)
  }
  scope :end_before, lambda { |end_date|
    where("array_length(dates, 1) > 0 AND dates[array_length(dates, 1)] <= ?", end_date.to_date)
  }
  scope :any_date_between, lambda { |start_date, end_date|
    where(<<~SQL.squish, start_date: start_date.to_date, end_date: end_date.to_date)
      array_length(dates, 1) > 0
      AND dates[array_length(dates, 1)] >= :start_date
      AND dates[1] <= :end_date
    SQL
  }
  scope :near_location, lambda { |lat, lng, radius|
    degree_distance = radius.to_f / 111.32
    where(latitude: (lat - degree_distance)..(lat + degree_distance))
      .where(longitude: (lng - degree_distance)..(lng + degree_distance))
      .where(<<~SQL.squish, lat: lat.to_f, lng: lng.to_f, radius: radius.to_f)
        (
          6371 * acos(
            cos(radians(:lat)) *
            cos(radians(latitude)) *
            cos(radians(longitude) - radians(:lng)) +
            sin(radians(:lat)) *
            sin(radians(latitude))
          )
        ) <= :radius
      SQL
  }
  scope :calendar_dates, lambda { |start_date|
    end_date = start_date.to_date + 6.months
    where(<<~SQL.squish, start_date: start_date.to_date, end_date: end_date)
      dates IS NOT NULL
      AND array_length(dates, 1) > 0
      AND (
        dates[1] <= :end_date
        AND dates[array_length(dates, 1)] >= :start_date
      )
    SQL
  }
  scope :full_text_search, lambda { |query|
    sanitized_query = sanitize_sql_array(["plainto_tsquery('pg_catalog.portuguese', ?)", query])
    where("search_vector @@ #{sanitized_query}")
      .order(Arel.sql("ts_rank(search_vector, #{sanitized_query}) DESC"))
  }

  before_save :ensure_dates_are_sorted

  def enable!
    update!(active: true, deactivated_at: nil)
  end

  def disable!
    update!(active: false, deactivated_at: Time.current)
  end

  def start_datetime
    dates.min
  end

  def end_datetime
    dates.max
  end

  private

  def ensure_dates_are_sorted
    self.dates = dates.sort if dates.present?
  end
end
