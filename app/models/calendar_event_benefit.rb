class CalendarEventBenefit < ApplicationRecord
  enum :redeemed_rule, {unlimited: 0, per_user: 1}

  belongs_to :calendar_event
  belongs_to :voucher_bucket, optional: true
  has_many :redeems, class_name: "CalendarEventBenefitRedeem"

  def redeem_for(user)
    return false unless can_redeem?(user)

    ActiveRecord::Base.transaction do
      voucher = voucher_bucket.vouchers.create!(
        began_at: event_date,
        expired_at: event_date,
        redeemed_at: Time.current,
        user_id: user.id
      )

      redeem = redeems.create!(voucher_id: voucher.id)
      redeem
    end
  rescue ActiveRecord::RecordInvalid
    false
  end

  def redeemed? = redeems.any?

  def usage_expires_at = event_date

  private

  def can_redeem?(user)
    return true if redeemed_rule == "unlimited"
    return true if redeems.joins(:voucher).where("voucher.user_id": user.id).count < redeemed_limit_per_user.to_i
    update!(available: false)

    false
  end
end
