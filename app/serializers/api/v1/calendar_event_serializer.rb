# frozen_string_literal: true

class Api::V1::CalendarEventSerializer < ActiveModel::Serializer
  attribute :id
  attributes :banner_url,
    :max_discount,
    :title,
    :description,
    :location_text,
    :latitude,
    :longitude,
    :start_datetime,
    :end_datetime,
    :tags,
    :benefits

  attribute :promoter, if: -> { object.calendar_event_promoter.present? }

  def location_text
    object.full_address || "#{object.address_street}, #{object.address_number} - #{object.address_city} - #{object.address_state}"
  end

  def start_datetime
    object.dates.min
  end

  def end_datetime
    object.dates.max
  end

  def benefits
    return unless @instance_options[:include_redeemed]

    object.event_benefits.map do |benefit|
      Api::V1::CalendarEventBenefitSerializer.new(benefit)
    end
  end

  def promoter
    Api::V1::CalendarEventPromoterSerializer.new(object.calendar_event_promoter)
  end
end
