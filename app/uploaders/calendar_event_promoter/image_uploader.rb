# frozen_string_literal: true

class CalendarEventPromoter::ImageUploader < CarrierWave::Uploader::Base
  include CarrierWave::RMagick

  version :large do
    process resize_to_fit: [1128, 550]
  end

  version :small do
    process resize_to_fit: [328, 160]
  end

  def store_dir
    "uploads/#{model.class.to_s.underscore}/#{mounted_as}/#{model.id}"
  end

  def extension_allowlist
    %w[png jpeg jpg]
  end

  def filename
    "#{secure_token}.#{file.extension}"
  end

  def default_url(*)
    "#{Rails.application.routes.default_url_options[:host]}/images/fallback/calendar_event_promoter/image/" + [version_name, "samplefile.jpg"].compact.join("_")
  end

  private

  def secure_token
    var = :"@#{mounted_as}_secure_token"
    model.instance_variable_get(var) or model.instance_variable_set(var, SecureRandom.uuid)
  end
end
