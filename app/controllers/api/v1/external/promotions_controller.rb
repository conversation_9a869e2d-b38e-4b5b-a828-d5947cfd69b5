# frozen_string_literal: true

class Api::V1::External::PromotionsController < Api::V1::BaseController
  before_action :guard_page
  before_action :guard_redeem_type

  def index
    promotions = Promotion
      .left_joins(cupons: :organization)
      .joins(cupons: [:branch, :organization])
      .preload(cupons: [:branch, :organization])
      .left_joins(:vouchers)
      .status_available
      .then { by_branch_type(_1) }
      .then { by_tags(_1) }
      .then { filter_by_category_ids(_1) }
      .merge(Cupon.active.available_for_business(current_business.project_ids))
      .merge(Branch.active)
      .then { apply_term_filter(_1) }
      .merge(Organization.active)
      .where.not(organizations: {id: blocklisted_organization_ids})
      .distinct
      .paginate(page: params[:page], per_page: params[:per_page])

    render json: promotions.preload(:branches, :base_tags),
      each_serializer: Api::V1::Promotions::IndexSerializer,
      business_cashback: current_business.cashback?,
      custom_text:,
      template:,
      current_business:,
      lat: params[:lat],
      lng: params[:lng]
  end

  private

  def guard_page
    return if params[:page].blank?

    return unless params[:page].to_i <= 0

    render json: {error: I18n.t("page.invalid"), status: :unprocessable_entity},
      status: :unprocessable_entity
  end

  def guard_redeem_type
    return if params[:redeem_type].blank?

    return if params[:redeem_type].in?(%w[physical online])

    render json: {error: I18n.t("promotions.redeem_type.invalid"), status: :unprocessable_entity},
      status: :unprocessable_entity
  end

  def custom_text
    current_business.custom_texts.find_by(message_type: Enums::CustomTextType::PROMOTION_CUSTOM_TEXT)
  end

  def template
    Enums::Promotion::SerializerTemplate::TEMPLATE.transform_values { |v| v[0] }
  end

  def category_ids_params
    multi_value_param(params[:category_ids].presence || params[:category_id])
  end

  def by_tags(relation)
    return relation if params[:tags].blank?

    relation.joins(:base_tags).where(taggings: {business_id: current_business.project_ids}, tags: {name: params[:tags]})
  end

  def blocklisted_organization_ids
    @blocklisted_organization_ids ||=
      Organization::Blocklisted.new(business_ids: current_business.project_ids).ids
  end

  def apply_term_filter(relation)
    return relation if params[:term].blank?

    relation.where(
      "unaccent(organizations.name) ILIKE unaccent(:pattern) OR " \
      "unaccent(promotions.title) ILIKE unaccent(:pattern) OR " \
      "unaccent(promotions.description) ILIKE unaccent(:pattern)", pattern: "%#{params[:term]}%"
    ).distinct
  end

  def by_branch_type(relation)
    return relation.merge(Branch.physical) if params[:redeem_type] == "physical"
    return relation.merge(Branch.online) if params[:redeem_type] == "online"
    relation
  end

  def filter_by_category_ids(relation)
    return relation if category_ids_params.blank?

    relation = relation.joins(:categories).where(categories: {id: category_ids_params})
  end
end
