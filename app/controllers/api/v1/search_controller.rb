# frozen_string_literal: true

class Api::V1::SearchController < Api::V1::BaseController
  before_action :guard_page
  before_action :guard_redeem_type

  def index
    @results = {organizations: serialized_organizations, promotions: serialized_promotions}

    render json: @results
  end

  private

  def serialized_promotions
    ActiveModelSerializers::SerializableResource.new(
      promotions.preload(:branches, :base_tags).load_async,
      each_serializer: Api::V1::Promotions::IndexSerializer,
      business_cashback: current_user.cashback?,
      custom_text: current_business.custom_texts.find_by(message_type: Enums::CustomTextType::PROMOTION_CUSTOM_TEXT),
      template: Enums::Promotion::SerializerTemplate::TEMPLATE.transform_values { |v| v[0] },
      current_business:,
      lat: params[:lat],
      lng: params[:lng]
    ).as_json
  end

  def promotions
    PromotionsQuery.call(
      lat: params[:lat],
      lng: params[:lng],
      redeem_type: params[:redeem_type],
      page: params[:page],
      business_ids: current_user.project_ids,
      distance_km:,
      category_ids: multi_value_param(params[:category_ids].presence || params[:category_id]),
      organization_id: params[:organization_id],
      term: params[:term],
      tags: current_user.tags
    )
  end

  def serialized_organizations
    ActiveModelSerializers::SerializableResource.new(
      organizations.load_async,
      each_serializer: Api::V1::Organization::IndexSerializer,
      current_business_accepts_cashback: current_user.cashback?,
      current_business:,
      user_favorited_organization_ids: current_user.favorite_organizations.pluck(:organization_id),
      business_id: current_business.id,
      mobile?: request.headers["Api-Secret"],
      lat: params[:lat],
      lng: params[:lng],
      use_original_images: current_business.id == (Rails.env.homologation? ? 8 : 414)
    ).as_json
  end

  def organizations
    branch_distance = Branch::Distance.new(km_range: distance_km, lat: params[:lat],
      lng: params[:lng])

    ids =
      params[:organization_id] ||
      if params[:term].present?
        org_ids =
          Organization
            .left_joins(:tags, :categories)
            .joins(:promotions)
            .merge(Promotion.status_available.where(business_id: [current_user.project_ids, nil]))
            .active
            .not_blocked(blocklisted_organization_ids)
            .and(
              Organization
                .where(all_projects: true)
                .or(
                  Organization
                    .where(id: OrganizationProfile.where(business:  current_user.project_ids).pluck(:organization_id))
                )
            )
            .then { by_lbc_giftcard(_1) }
            .with_branches(params[:redeem_type])
            .by_term(params[:term].strip)
        if params[:category_id].present?
          org_ids =
            org_ids
              .merge(
                OrganizationCategory
                  .where(categories: {active: true, business_id: [current_user.project_ids, nil]})
                  .where(category_id: multi_value_param(params[:category_ids].presence || params[:category_id]))
              )
        end
        org_ids
          .distinct
          .pluck(:id)
      else
        Organization.cached_redeemable_ids(
          business_ids: current_user.project_ids,
          branch_type: params[:redeem_type],
          category_id: multi_value_param(params[:category_ids].presence || params[:category_id])
        )
      end

    Organization::WithBranches
      .new(distance: branch_distance, branch_type: params[:redeem_type])
      .all_organizations
      .includes(:organization_profiles, :categories)
      .where(id: ids)
      .page(params[:page])
  end

  def by_lbc_giftcard(relation)
    if Business.where(id: current_user.project_ids, lbc_giftcard: false).empty?
      return relation
    end

    relation.merge(Promotion.where.not(provider: Promotion::Provider::LBC_GIFTCARD))
  end

  def blocklisted_organization_ids
    @blocklisted_organization_ids ||=
      Organization::Blocklisted.new(business_ids: current_user.project_ids).ids
  end

  def guard_page
    return if params[:page].blank?

    return unless params[:page].to_i <= 0

    render json: {error: I18n.t("page.invalid"), status: :unprocessable_entity},
      status: :unprocessable_entity
  end

  def guard_redeem_type
    return if params[:redeem_type].blank?

    return if params[:redeem_type].in?(%w[physical online])

    render json: {error: I18n.t("promotions.redeem_type.invalid"), status: :unprocessable_entity},
      status: :unprocessable_entity
  end
end
