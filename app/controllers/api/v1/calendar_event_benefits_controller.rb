class Api::V1::CalendarEventBenefitsController < Api::V1::BaseController
  before_action :set_event_benefit

  def redeemed
    render json: @event_benefit.redeems, each_serializer: Api::V1::CalendarEventBenefit::RedeemSerializer
  end

  def redeem
    if (result = @event_benefit.redeem_for(current_user))
      render json: result, serializer: Api::V1::CalendarEventBenefit::RedeemSerializer, status: :created
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  private

  def set_event_benefit
    @event_benefit = CalendarEventBenefit
      .joins(:calendar_event)
      .where("calendar_events.business_id": current_business.id)
      .find(params[:benefit_id])
  end
end
