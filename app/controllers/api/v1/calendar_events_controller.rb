class Api::V1::CalendarEventsController < Api::V1::BaseController
  before_action :set_calendar_event, only: [:show]

  def index
    @events = current_business.calendar_events.active
    @events = @events.full_text_search(filter_params[:term]) if filter_params[:term].present?
    @events = events_by_date if filter_params[:start_date].present? || filter_params[:end_date].present?
    @events = events_by_location if filter_params[:lat].present? && filter_params[:lng].present?
    @events = @events.tagged_with(filter_params[:tags]) if filter_params[:tags].present?
    @events = @events.order(Arel.sql(sorting_order))

    render json: @events, each_serializer: Api::V1::CalendarEventSerializer
  end

  def show
    render json: @calendar_event,
      include_redeemed: filter_params[:include_redeemed],
      serializer: Api::V1::CalendarEventSerializer
  end

  def calendar
    start_date = filter_params[:initial_month] ? Date.parse(filter_params[:initial_month]) : Date.current
    6.months

    event_days =
      current_business
        .calendar_events
        .calendar_dates(start_date)
        .pluck("unnest(dates) as event_date")
        .map { |date| date.to_date.iso8601 }
        .uniq
        .sort

    render json: {
      month: start_date.strftime("%Y-%m"),
      event_days: event_days
    }
  end

  private

  def set_calendar_event
    @calendar_event = current_business.calendar_events.find(params[:id])
  end

  def filter_params
    params.permit(:term, :initial_month, :start_date, :end_date, :lat, :lng, :radius, :include_redeemed, :order_by, tags: [])
  end

  def sorting_order
    return "dates[1] ASC" unless filter_params[:order_by]

    order_params = filter_params[:order_by].to_s.split(",").filter_map do |criteria|
      CalendarEvent::ORDER_MAPPINGS[criteria]
    end.join(", ")

    order_params.presence || "publish_at ASC"
  end

  def events_by_date
    start_date = filter_params[:start_date] || Time.zone.now.beginning_of_day.iso8601

    if filter_params[:end_date].present?
      @events.any_date_between(start_date, filter_params[:end_date])
    else
      @events.start_after(filter_params[:start_date])
    end
  end

  def events_by_location
    @events.near_location(
      filter_params[:lat].to_f,
      filter_params[:lng].to_f,
      filter_params[:radius].to_i
    )
  end
end
