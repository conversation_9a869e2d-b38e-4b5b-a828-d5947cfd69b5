# frozen_string_literal: true

class Api::V2::PaymentsController < Api::V2::BaseController
  before_action -> { authorize! with: Api::V2::Business::RewardPolicy }

  def create
    reward = current_business.rewards.available.find(params[:product_id])
    payment = Order.one_click_buy(user: current_user, product: reward)

    if payment.captured?
      render json: payment.order, serializer: Api::V2::Payments::CreateSerializer, status: :created
    else
      render_error(error: payment.reason_denied, status_code: :unprocessable_entity)
    end
  rescue Voucher::NotAvailableError => e
    render_error(error: e.message, status_code: :unprocessable_entity)
  end
end
