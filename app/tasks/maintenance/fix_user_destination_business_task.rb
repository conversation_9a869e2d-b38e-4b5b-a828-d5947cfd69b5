# frozen_string_literal: true

module Maintenance
  class FixUserDestinationBusinessTask < MaintenanceTasks::Task
    def collection
      User
        .joins(subscriptions: :plan)
        .where(subscriptions: {active: true})
        .where("users.business_id != plans.destination_business_id")
        .where.not(plans: {destination_business: nil})
        .distinct
    end

    def process(user)
      subscription = user.subscriptions.active.take!

      user.update_columns(business_id: subscription.plan.destination_business_id)
    end
  end
end
