# frozen_string_literal: true

module Maintenance
  class CreateVouchersForRewardsTask < MaintenanceTasks::Task
    attribute :reward_id, :integer
    attribute :voucher_quantity, :integer
    validates :reward_id, :voucher_quantity, presence: true

    no_collection

    def process
      raise if Rails.env.production?

      reward = Reward.find(reward_id)
      reward.create_voucher_bucket if reward.voucher_bucket.blank?
      voucher_quantity.times do
        reward.voucher_bucket.vouchers.create!(
          code: SecureRandom.uuid,
          began_at: DateTime.now,
          expired_at: DateTime.now + 10.years
        )
      end
    end
  end
end
