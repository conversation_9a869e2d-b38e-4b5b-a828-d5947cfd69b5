# frozen_string_literal: true

module Maintenance
  class FixSubscriptionAuthUserBusinessTask < MaintenanceTasks::Task
    def collection
      AuthorizedUser
        .joins(:user, main_business: :subscription_config)
        .where("users.business_id != authorized_users.business_id")
    end

    def process(authorized_user)
      business_id = authorized_user.user.business_id
      authorized_user.update_columns(business_id:)
    end
  end
end
