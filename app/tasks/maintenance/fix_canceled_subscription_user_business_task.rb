# frozen_string_literal: true

module Maintenance
  class FixCanceledSubscriptionUserBusinessTask < MaintenanceTasks::Task
    def collection
      Subscription
        .joins(:user, :subscription_config)
        .where(active: false)
        .where("retries >= 3")
        .where.not(subscription_configs: {cancel_destination_business: nil})
        .where("users.business_id != subscription_configs.cancel_destination_business_id")
        .distinct
    end

    def process(subscription)
      subscription.user.update!(business: subscription.subscription_config.cancel_destination_business)
    end
  end
end
