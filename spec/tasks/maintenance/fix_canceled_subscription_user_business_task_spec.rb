# frozen_string_literal: true

require "rails_helper"

module Maintenance
  RSpec.describe FixCanceledSubscriptionUserBusinessTask do
    let!(:business) { create(:business) }
    let!(:business_lower_plan) { create(:business, main_business: business) }
    let!(:subscription_config) { create(:subscription_config, business:, cancel_destination_business: business_lower_plan) }
    let!(:plan) { create(:plan, business:, subscription_config:) }

    describe "#collection" do
      let!(:user_error) { create(:user, business:) }
      let!(:subscription_error) { create(:subscription, user: user_error, subscription_config:, plan:, active: false, retries: 3) }

      let!(:user_ok) { create(:user, business:) }
      let!(:subscription_ok) { create(:subscription, user: user_ok, subscription_config:, plan:, active: true, retries: 2) }

      it "returns subscriptions whose user is in the wrong business" do
        expect(described_class.collection)
          .to include(subscription_error)
          .and not_include(subscription_ok)
      end
    end

    describe "#process" do
      let!(:user) { create(:user, business:) }
      let!(:subscription) { create(:subscription, user:, subscription_config:, plan:, active: false, retries: 3) }

      it "fixes user business" do
        expect { described_class.process(subscription) }
          .to change { user.reload.business }.to(business_lower_plan)
      end
    end
  end
end
