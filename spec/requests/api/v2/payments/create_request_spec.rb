# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V2::PaymentsController, type: :request do
  let(:currency) { :points }
  let(:kind) { Wallet::Kind::CASHBACK }
  let!(:business) { create(:business, reward: reward_flag, currency:, fair_value: 0.0125) }
  let!(:user) { create(:user, business:) }
  let!(:voucher_bucket) { create(:voucher_bucket) }
  let!(:voucher) { create(:voucher, bucket: voucher_bucket) }
  let!(:reward) { create(:reward, voucher_bucket:, business:) }

  let!(:business_wallet) { create(:wallet, currency:, kind:, business:, balance: 10000) }
  let!(:outflow_wallet) { create(:wallet, :outflow, currency:, kind:, balance: 20000) }

  let(:headers) do
    Devise::JWT::TestHelpers.auth_headers({"Api-Secret" => business.api_secret, "aud" => "api"}, user)
  end
  let(:params) { {product_id: reward.id, product_type: "reward"} }

  context "when user has balance" do
    let!(:reward_flag) { true }
    let!(:funds) do
      [
        create(:fund, :points, :available, user:, credited: true, amount: 200, expires_at: 1.week.from_now),
        create(:fund, :points, :available, user:, credited: true, amount: 50, expires_at: 1.day.from_now)
      ]
    end
    let!(:user_wallet) { create(:wallet, currency:, kind:, user:, balance: 2345) }
    let!(:expected_amount) { 100 }

    it "renders created" do
      expect { post("/api/v2/payments", headers:, params:) }
        .to change { user_wallet.reload.balance }.by(-expected_amount)
        .and change { outflow_wallet.reload.balance }.by(expected_amount)
        .and not_change { business_wallet.reload.balance }
        .and change(reward.orders.where(status: Order::Status::COMPLETED), :count).by(1)
        .and change(Payment.captured.where(value: expected_amount), :count).by(1)
        .and change { funds[1].reload.remaining_amount }.to(0)
        .and change { funds[0].reload.remaining_amount }.to(150)
        .and change { voucher.reload.order_id }.to(be_present)

      expect(response).to be_created
      expect(response.parsed_body).to include("order_id", "usage_instruction")
    end
  end

  context "when user does not have balance" do
    let!(:reward_flag) { true }
    let!(:funds) do
      [
        create(:fund, :points, :available, user:, credited: true, amount: 1698, expires_at: 1.week.from_now),
        create(:fund, :points, :available, user:, credited: true, amount: 300, expires_at: 1.day.from_now)
      ]
    end
    let!(:user_wallet) { create(:wallet, currency:, kind:, user:, balance: 99) }

    it "renders unprocessable" do
      expect { post("/api/v2/payments", headers:, params:) }
        .to not_change { user_wallet.reload.balance }
        .and not_change { outflow_wallet.reload.balance }
        .and not_change { business_wallet.reload.balance }
        .and not_change(Order, :count)
        .and not_change(Payment, :count)
        .and not_change { funds[1].reload.remaining_amount }
        .and not_change { funds[0].reload.remaining_amount }
        .and not_change { voucher.reload.order_id }

      expect(response).to be_unprocessable
      expect(response.parsed_body).to eq "error" => "Saldo insuficiente"
    end
  end

  context "when business doesnt have reward" do
    let!(:reward_flag) { false }

    it "returns forbidden" do
      post("/api/v2/payments", headers:, params:)

      expect(response).to be_forbidden
    end
  end
end
