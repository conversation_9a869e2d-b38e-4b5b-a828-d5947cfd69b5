# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V1::CalendarEventsController, type: :request do
  let!(:business) { create(:business) }
  let(:user) { create(:user, business:) }
  let(:headers) { user.create_new_auth_token.merge("Api-Secret": business.api_secret) }

  let!(:calendar_event_first) { create(:calendar_event, business:, dates: [Date.current, Date.current + 1.day]) }
  let!(:calendar_event_second) { create(:calendar_event, business:, dates: [Date.current + 2.days]) }
  let!(:calendar_event_other_business) { create(:calendar_event) }
  let!(:benefit) { create(:calendar_event_benefit, calendar_event: calendar_event_first) }

  let(:calendar_event_serializer_keys) do
    %w[banner_url benefits description end_datetime id latitude location_text longitude max_discount promoter start_datetime tags title]
  end

  let(:calendar_event_benefit_serializer_keys) do
    %w[id event_date discount_percentage available user_redeemed redeemed_event_benefits]
  end

  describe "GET /api/v1/calendar_events" do
    it "returns active calendar events matching filters" do
      get "/api/v1/calendar_events",
        headers:,
        params: {
          start_date: Date.current,
          end_date: Date.current + 7.days
        },
        as: :json

      expect(response).to have_http_status(:ok)
      json = response.parsed_body

      expect(json).to be_an(Array)
      expect(json.size).to eq(2)
      expect(json.first.keys).to match_array(calendar_event_serializer_keys)
      ids = json.map { |e| e["id"] }
      expect(ids).to include(calendar_event_first.id, calendar_event_second.id)
      expect(ids).not_to include(calendar_event_other_business.id)
    end

    context "with full_text_search" do
      let!(:matching_event) { create(:calendar_event, business:, title: "Amazing Show") }
      let!(:non_matching_event) { create(:calendar_event, business:, title: "Boring Event") }

      it "returns only matching events" do
        get api_v1_calendar_events_path, headers:, params: {term: "Amazing"}
        titles = response.parsed_body.map { |e| e["title"] }

        expect(response).to be_successful
        expect(titles).to include("Amazing Show")
        expect(titles).not_to include("Boring Event")
      end
    end

    context "with start_after" do
      let!(:future_event) { create(:calendar_event, business:, dates: [6.days.from_now]) }
      let!(:past_event) { create(:calendar_event, business:, dates: [2.days.ago]) }

      it "returns only events starting after the given date" do
        get api_v1_calendar_events_path, headers:, params: {start_date: Time.zone.today}
        ids = response.parsed_body.map { |e| e["id"] }

        expect(response).to be_successful
        expect(ids).to include(future_event.id)
        expect(ids).not_to include(past_event.id)
      end
    end

    context "with end_before" do
      let!(:soon_event) { create(:calendar_event, business:, dates: [1.day.from_now, 3.days.from_now]) }
      let!(:late_event) { create(:calendar_event, business:, dates: [30.days.from_now, 32.days.from_now]) }

      it "returns only events ending before the given date" do
        get api_v1_calendar_events_path, headers:, params: {end_date: 10.days.from_now}
        ids = response.parsed_body.map { |e| e["id"] }

        expect(response).to be_successful
        expect(ids).to include(soon_event.id)
        expect(ids).not_to include(late_event.id)
      end
    end

    context "with near_location" do
      let!(:nearby_event) { create(:calendar_event, business:, latitude: 10.0, longitude: 10.0) }
      let!(:far_event) { create(:calendar_event, business:, latitude: 50.0, longitude: 50.0) }

      it "returns only nearby events" do
        get api_v1_calendar_events_path, headers:, params: {lat: 10.0, lng: 10.0, radius: 10}
        ids = response.parsed_body.map { |e| e["id"] }

        expect(response).to be_successful
        expect(ids).to include(nearby_event.id)
        expect(ids).not_to include(far_event.id)
      end
    end

    context "with tags" do
      let!(:tag) { create(:tag, name: "music") }
      let!(:tagged) { create(:calendar_event, business:, tags: [tag]) }
      let!(:untagged) { create(:calendar_event, business:) }

      it "returns only tagged events" do
        get api_v1_calendar_events_path, headers:, params: {tags: ["music"]}
        ids = response.parsed_body.map { |e| e["id"] }

        expect(response).to be_successful
        expect(ids).to include(tagged.id)
        expect(ids).not_to include(untagged.id)
      end
    end

    context "with sorting" do
      let!(:calendar_event_first) { create(:calendar_event, business:, redeemed_amount: 100, max_discount: 10, dates: [2.days.from_now]) }
      let!(:calendar_event_second) { create(:calendar_event, business:, redeemed_amount: 50, max_discount: 50, dates: [1.day.from_now]) }

      it "sorts by popularity" do
        get api_v1_calendar_events_path, headers:, params: {order_by: "popularity"}
        ids = response.parsed_body.map { |e| e["id"] }.take(2)

        expect(ids).to eq([calendar_event_first.id, calendar_event_second.id])
      end

      it "sorts by max_discount" do
        get api_v1_calendar_events_path, headers:, params: {order_by: "max_discount"}
        ids = response.parsed_body.map { |e| e["id"] }

        expect(ids).to eq([calendar_event_second.id, calendar_event_first.id])
      end

      it "sorts by min_discount" do
        get api_v1_calendar_events_path, headers:, params: {order_by: "min_discount"}
        ids = response.parsed_body.map { |e| e["id"] }

        expect(ids).to eq([calendar_event_first.id, calendar_event_second.id])
      end

      it "sorts by date" do
        get api_v1_calendar_events_path, headers:, params: {order_by: "date"}
        ids = response.parsed_body.map { |e| e["id"] }

        expect(ids).to eq([calendar_event_second.id, calendar_event_first.id])
      end
    end

    context "with start_date and end_date overlapping event dates" do
      let!(:inside_range_event) { create(:calendar_event, business:, dates: [5.days.from_now, 10.days.from_now]) }
      let!(:overlapping_event)  { create(:calendar_event, business:, dates: [2.days.from_now, 6.days.from_now]) }
      let!(:outside_event)      { create(:calendar_event, business:, dates: [20.days.from_now, 30.days.from_now]) }

      it "includes events with any dates overlapping the range" do
        get api_v1_calendar_events_path,
          headers:,
          params: {
            start_date: 4.days.from_now.to_date,
            end_date: 12.days.from_now.to_date
          }

        ids = response.parsed_body.map { |e| e["id"] }

        expect(ids).to include(inside_range_event.id, overlapping_event.id)
        expect(ids).not_to include(outside_event.id)
      end
    end

    context "with calendar event promoter" do
      let!(:promoter) { create(:calendar_event_promoter, calendar_events: [calendar_event_first]) }

      it "includes promoter in the response" do
        get "/api/v1/calendar_events/#{calendar_event_first.id}",
          headers:,
          as: :json

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body["promoter"]).to be_present
        expect(response.parsed_body["promoter"]).to include("id", "name", "banner_url")
      end
    end
  end

  describe "GET /api/v1/calendar_events/:id" do
    it "returns the specific calendar event" do
      get "/api/v1/calendar_events/#{calendar_event_first.id}",
        headers:,
        as: :json

      expect(response).to have_http_status(:ok)
      json = response.parsed_body

      expect(json.keys).to match_array(calendar_event_serializer_keys)
      expect(json["id"]).to eq(calendar_event_first.id)
    end

    it "returns 404 for an event not belonging to the business" do
      get "/api/v1/calendar_events/#{calendar_event_other_business.id}",
        headers:,
        as: :json

      expect(response).to have_http_status(:not_found)
    end

    context "when event has benefits" do
      it "returns the correct JSON structure for benefits" do
        get "/api/v1/calendar_events/#{calendar_event_first.id}",
          headers:,
          params: {include_redeemed: true},
          as: :json

        expect(response).to have_http_status(:ok)
        json_response = response.parsed_body
        benefits = json_response["benefits"]

        first_benefit = benefits.first
        expect(first_benefit.keys).to match_array(calendar_event_benefit_serializer_keys)

        expect(first_benefit["id"]).to eq(benefit.id)
        expect(first_benefit["event_date"]).to eq(benefit.event_date.iso8601)
        expect(first_benefit["discount_percentage"]).to eq(benefit.discount_percentage)
        expect(first_benefit["available"]).to be_in([true, false])
        expect(first_benefit["user_redeemed"]).to be_in([true, false])
      end
    end
  end

  describe "GET /api/v1/calendar_events/calendar" do
    it "returns a calendar summary with event days for 6 months window" do
      get "/api/v1/calendar_events/calendar",
        headers:,
        params: {initial_month: Date.current.strftime("%Y-%m-%d")},
        as: :json

      expect(response).to have_http_status(:ok)
      json = response.parsed_body

      expect(json).to include("month", "event_days")
      expect(json["month"]).to eq(Date.current.strftime("%Y-%m"))
      expect(json["event_days"]).to be_an(Array)
      expect(json["event_days"]).to include(Date.current.iso8601)
    end

    it "returns an empty event_days array if no events exist in the window" do
      get "/api/v1/calendar_events/calendar",
        headers:,
        params: {initial_month: "2100-01-01"},
        as: :json

      expect(response).to have_http_status(:ok)
      json = response.parsed_body

      expect(json["event_days"]).to eq([])
    end
  end
end
