# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::V1::CalendarEventBenefitsController, type: :request do
  let(:business) { create(:business) }
  let(:user) { create(:user, business:) }
  let(:auth_headers) { user.create_new_auth_token.merge("Api-Secret" => business.api_secret) }

  let(:calendar_event) { create(:calendar_event, business:) }
  let(:voucher_bucket) { create(:voucher_bucket) }

  let(:event_benefit) do
    create(:calendar_event_benefit, calendar_event:, voucher_bucket:, redeemed_rule: :per_user, redeemed_limit_per_user: 1)
  end

  describe "POST /api/v1/calendar_events/:calendar_event_id/benefits/:id/redeem" do
    context "when user is allowed to redeem" do
      it "creates a redeem and returns created status" do
        post "/api/v1/calendar_events/#{calendar_event.id}/benefits/#{event_benefit.id}/redeem",
          headers: auth_headers,
          as: :json

        expect(response).to have_http_status(:created)
        expect(response.parsed_body.keys).to match_array(%w[id code redeemed_at expires_at purchase_url])
      end
    end

    context "when user exceeds redemption limit" do
      before do
        event_benefit.redeem_for(user)
      end

      it "returns 422 unprocessable entity" do
        post "/api/v1/calendar_events/#{calendar_event.id}/benefits/#{event_benefit.id}/redeem",
          headers: auth_headers,
          as: :json

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "GET /api/v1/calendar_event_benefits/:id/redeemed" do
    before do
      event_benefit.redeem_for(user)
    end

    it "returns list of redeems" do
      get "/api/v1/calendar_events/#{calendar_event.id}/benefits/#{event_benefit.id}/redeemed",
        headers: auth_headers,
        as: :json

      expect(response).to have_http_status(:ok)

      body = response.parsed_body
      expect(body).to be_an(Array)
      expect(body.first.keys).to match_array(%w[id code redeemed_at expires_at purchase_url])
    end
  end
end
