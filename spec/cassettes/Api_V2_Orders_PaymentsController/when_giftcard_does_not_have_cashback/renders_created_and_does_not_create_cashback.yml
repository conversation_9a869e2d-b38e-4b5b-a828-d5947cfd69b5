---
http_interactions:
- request:
    method: post
    uri: https://apisandboxpay.globalpaysolucoes.com.br/api/transactions/v1/payments/card
    body:
      encoding: UTF-8
      string: >-
        {
          "amount": "50.21",
          "paymentCardType": "credit",
          "installments": "1",
          "antifraudCode": "fc10b881-d9a0-4ab1-a6fd-a102db188f49",
          "card": {
            "number": "****************",
            "holderName": "ADRIANO P SOUZA",
            "cvv": "321",
            "expirationMonth": "12",
            "expirationYear": "25""number": "****************",
            "holderName": "ADRIANO P SOUZA",
            "cvv": "321",
            "expirationMonth": "12",
            "expirationYear": "25"
          },
          "customer": {
            "name": "ADRIANO PESSOA SOUZA",
            "documentType": "PF",
            "documentNumber": "90936034009",
            "email": "<EMAIL>",
            "phoneNumber": "31999855214",
            "cellPhoneNumber": "31999855214",
            "address": {
              "street": "Rua Jose Lacerda",
              "number": "55",
              "complement": "APT 104 BL 02",
              "neighborhood": "Trevo",
              "city": "Belo Horizonte",
              "state": "MG",
              "zipCode": "45810000",
              "country": "BR"
            }
          },
          "itens": [
            {
              "unitPrice": "50.21",
              "productName": "giftcard",
              "quantity": "1"
            }
          ],
          "deviceInfo": {
            "ipAddress": "*************"
          }
        }
    headers:
      User-Agent:
      - Faraday v2.12.2
      Authorization:
      - Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3N1ZXIiOiI5NGZlOWI5ZS0wMmM4LTQ5MTctOGFjZC0xNTMwMWM1NDdkMmEiLCJlY19jbGllbnQiOnsiaWQiOiI0MTk1MzE3IiwiY29tcGFueU5hbWUiOiJMRUNVUE9OIFMuQS4iLCJkb2N1bWVudE51bWJlciI6IjI2OTg5Njk3MDAwMTY5In0sImlhdCI6MTczNjYxNjg2MCwiZXhwIjoxNzM2NjE3MTYwfQ.UBbfwI2Q8BLnBLfvtCB6mbzBmAaH0bE12cg0eHkAIDo
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 201
      message: Created
    headers:
      Server:
      - nginx/1.22.0 (Ubuntu)
      Date:
      - Sat, 11 Jan 2025 17:34:28 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '536'
      Connection:
      - keep-alive
      X-Powered-By:
      - Express
      Vary:
      - Origin
      Etag:
      - W/"218-kiG1fQXOVOupxXpfCa56nP6Y1Dw"
    body:
      encoding: UTF-8
      string: >-
        {
          "status": 201,
          "message": "Pay Credit Card",
          "data": {
            "orderCode": "561cb534a5304",
            "paymentId": "020091487401111734280002361881640000000000",
            "authorizationCode": "955726",
            "statusText": "PAID",
            "amount": "50.21",
            "gateway": {
              "paymentAuthorization": {
                "returnCode": "0",
                "description": "Sucesso",
                "paymentId": "020091487401111734280002361881640000000000",
                "authorizationCode": "955726",
                "orderNumber": "561cb534a5304",
                "amount": 5021,
                "releaseAt": "2025-01-11T14:34:28.2605125-03:00"
              }
            }
          },
          "id": "4195317",
          "companyName": "LECUPON S.A.",
          "documentNumber": "26989697000169"
        }
  recorded_at: Sat, 11 Jan 2025 17:34:28 GMT
recorded_with: VCR 6.3.1
