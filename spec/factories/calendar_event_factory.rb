FactoryBot.define do
  factory :calendar_event do
    title { "Event #{rand(1000)}" }
    description { FFaker::LoremBR.paragraph(rand(50..100)) }
    max_discount { rand(10..70) }
    dates { [Time.zone.today, Time.zone.today + 1.day] }
    latitude { -23.5505 }
    longitude { -46.6333 }
    redeemed_amount { 0 }
    active { true }
    business
    calendar_event_promoter

    trait :inactive do
      active { false }
      deactivated_at { Time.current }
    end

    trait :with_tags do
      tag_list { ["music", "festival"] }
    end
  end
end
