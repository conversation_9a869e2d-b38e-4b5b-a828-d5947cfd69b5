require "rails_helper"

RSpec.describe CalendarEventBenefit, type: :model do
  describe "defaults" do
    it "has discount_percentage default to 0" do
      benefit = described_class.new
      expect(benefit.discount_percentage).to eq(0)
    end

    it "has available default to true" do
      benefit = described_class.new
      expect(benefit.available).to eq(true)
    end
  end

  describe "#redeem_for" do
    subject(:benefit) do
      described_class.create!(
        calendar_event: calendar_event,
        voucher_bucket: voucher_bucket,
        event_date: event_date,
        redeemed_rule: :unlimited,
        redeemed_limit_per_user: 1
      )
    end

    let(:user) { create(:user) }
    let(:calendar_event) { create(:calendar_event) }
    let(:voucher_bucket) { create(:voucher_bucket) }
    let(:event_date) { Time.zone.today }

    context "when rule is unlimited" do
      it "creates a voucher and redeem" do
        expect {
          result = benefit.redeem_for(user)
          expect(result).to be_present
          expect(result.voucher).to be_present
        }.to change(Voucher, :count).by(1)
          .and change(CalendarEventBenefitRedeem, :count).by(1)
      end
    end

    context "when rule is per_user and limit not reached" do
      before { benefit.update!(redeemed_rule: :per_user, redeemed_limit_per_user: 2) }

      it "allows redemption" do
        expect {
          result = benefit.redeem_for(user)
          expect(result).to be_present
        }.to change(CalendarEventBenefitRedeem, :count).by(1)
      end
    end

    context "when rule is per_user and limit is reached" do
      before do
        voucher_bucket = create(:voucher_bucket)
        benefit.update!(redeemed_rule: :per_user, redeemed_limit_per_user: 1)
        benefit.redeems.create!(voucher: create(:voucher, voucher_bucket_id: voucher_bucket.id, user_id: user.id))
      end

      it "does not allow redemption" do
        expect(benefit.redeem_for(user)).to eq(false)
      end
    end
  end

  describe "#redeemed?" do
    let(:benefit) { create(:calendar_event_benefit) }

    it "returns true when there are redeems" do
      voucher_bucket = create(:voucher_bucket)
      benefit.redeems.create!(voucher: create(:voucher, voucher_bucket_id: voucher_bucket.id))
      expect(benefit.redeemed?).to eq(true)
    end

    it "returns false when there are no redeems" do
      expect(benefit.redeemed?).to eq(false)
    end
  end

  describe "#usage_expires_at" do
    it "returns the event_date" do
      date = Date.current
      benefit = described_class.new(event_date: date)
      expect(benefit.usage_expires_at).to eq(date)
    end
  end
end
