require "rails_helper"

RSpec.describe Order, type: :model do
  let!(:user) { create(:user) }
  let!(:voucher_bucket) { create(:voucher_bucket) }
  let!(:reward) { create(:reward, :with_vouchers, quantity: 3, voucher_bucket:) }
  let!(:redeem_limit) { create(:redeem_rule, :redeem_limit, reward:, value: 1) }

  context "when user didnt reach reedem_limit" do
    it "is valid" do
      order = build(:order, :with_reward, reward:, user:, voucher: reward.vouchers[0])
      expect(order).to be_valid
    end
  end

  context "when user reached redeem_limit" do
    let!(:completed_order) { create(:order, :with_reward, :completed, user:, reward:, redeemed_at: 10.days.ago, voucher: reward.vouchers[1]) }

    it "is invalid" do
      order = build(:order, :with_reward, reward:, user:, voucher: reward.vouchers[2])

      expect(order).to be_invalid
    end
  end
end
