# spec/models/calendar_event_spec.rb
require "rails_helper"

RSpec.describe CalendarEvent, type: :model do
  let(:business) { create(:business) }
  let(:promoter) { create(:calendar_event_promoter) }
  let(:event) { create(:calendar_event, business: business, calendar_event_promoter: promoter) }

  describe "scopes" do
    describe ".start_after" do
      let!(:future_event) { create(:calendar_event, dates: [Time.zone.today + 1.week]) }
      let!(:past_event) { create(:calendar_event, dates: [Time.zone.today - 1.week]) }

      it "returns events starting after specified date" do
        expect(CalendarEvent.start_after(Time.zone.today)).to include(future_event)
        expect(CalendarEvent.start_after(Time.zone.today)).not_to include(past_event)
      end
    end

    describe ".end_before" do
      let!(:early_event) { create(:calendar_event, dates: [Time.zone.today]) }
      let!(:late_event) { create(:calendar_event, dates: [Time.zone.today + 1.week]) }

      it "returns events ending before specified date" do
        expect(CalendarEvent.end_before(Time.zone.today + 3.days)).to include(early_event)
        expect(CalendarEvent.end_before(Time.zone.today + 3.days)).not_to include(late_event)
      end
    end

    describe ".by_popularity" do
      let!(:popular_event) { create(:calendar_event, redeemed_amount: 100) }
      let!(:unpopular_event) { create(:calendar_event, redeemed_amount: 5) }

      it "orders by redeemed_amount descending" do
        expect(CalendarEvent.by_popularity.first).to eq(popular_event)
      end
    end

    describe ".near_location" do
      let!(:near_event) { create(:calendar_event) }
      let!(:far_event) { create(:calendar_event) }

      before do
        near_event.update!(latitude: -23.5505, longitude: -46.6333)
        far_event.update!(latitude: -22.9068, longitude: -43.1729)
      end

      it "returns events near specified location" do
        results = CalendarEvent.near_location(-23.5505, -46.6333, 10)
        expect(results).to include(near_event)
        expect(results).not_to include(far_event)
      end
    end

    describe ".calendar_dates" do
      let!(:matching_event) { create(:calendar_event, dates: [Time.zone.today + 1.week]) }
      let!(:non_matching_event) { create(:calendar_event, dates: [Time.zone.today + 7.months]) }

      it "returns events within date range" do
        results = CalendarEvent.calendar_dates(Time.zone.today)
        expect(results).to include(matching_event)
        expect(results).not_to include(non_matching_event)
      end
    end

    describe ".full_text_search" do
      let!(:music_event) { create(:calendar_event, title: "Music Festival", description: "Best music event") }
      let!(:food_event) { create(:calendar_event, title: "Food Fair", description: "Gourmet experience") }

      it "returns matching events" do
        results = CalendarEvent.full_text_search("Music")
        expect(results).to include(music_event)
        expect(results).not_to include(food_event)
      end
    end
  end

  describe "instance methods" do
    describe "#enable!" do
      it "activates the event" do
        event.update(active: false)
        expect { event.enable! }.to change(event, :active).to(true)
        expect(event.deactivated_at).to be_nil
      end
    end

    describe "#disable!" do
      it "deactivates the event" do
        expect { event.disable! }.to change(event, :active).to(false)
        expect(event.deactivated_at).not_to be_nil
      end
    end

    describe "#start_datetime" do
      let(:event) { create(:calendar_event, dates: [Time.zone.today + 1.day, Time.zone.today]) }

      it "returns earliest date" do
        expect(event.start_datetime).to eq(Time.zone.today.beginning_of_day)
      end
    end

    describe "#end_datetime" do
      let(:event) { create(:calendar_event, dates: [Time.zone.today, Time.zone.today + 3.days]) }

      it "returns latest date" do
        expect(event.end_datetime).to eq(Time.zone.today.beginning_of_day + 3.days)
      end
    end
  end

  describe "callbacks" do
    describe "#ensure_dates_are_sorted" do
      let!(:unsorted_dates) { [Time.zone.today.end_of_day + 1.week, Time.zone.today.end_of_day] }

      it "sorts dates before save" do
        event = create(:calendar_event, dates: unsorted_dates)
        expect(event.dates.first.to_date).to eq(unsorted_dates.min.to_date)
      end
    end
  end
end
