require "rails_helper"

RSpec.describe Webhook::Betalabs::EventHandler do
  let(:business) { create(:business) }
  let(:handler) { described_class.new }

  describe ".parse" do
    let(:subscription_response_data) do
      double("subscription_response", body: {
        "data" => {
          "id" => 2,
          "plan_id" => 1,
          "status" => "active",
          "consumer" => {
            "id" => 5,
            "full_name" => "<PERSON>",
            "email" => "<EMAIL>",
            "document1" => "***********",
            "telephone" => {
              "ddd" => "31",
              "number" => "975577723"
            }
          }
        }
      }.to_json, success?: true)
    end
    let(:request) { double("request", params: {"id" => 2}) }

    before do
      allow(Betalabs::Api).to receive(:new).and_return(double("betalabs_api", subscription: subscription_response_data))
    end

    context "when is a active subscription" do
      it "must call Webhook::Event::ActiveSubscription" do
        allow(Webhook::Betalabs::Events::ActiveSubscription).to receive(:new).and_return(double("action", action: nil))
        handler.parse(business:, request:)
        expect(Webhook::Betalabs::Events::ActiveSubscription).to have_received(:new)
      end
    end

    context "when is a event of canceled payment" do
      let(:subscription_response_data) do
        double("subscription_response", body: {
          "data" => {
            "id" => 2,
            "plan_id" => 1,
            "status" => "inactive",
            "consumer" => {
              "id" => 5,
              "full_name" => "Lucas Santos",
              "email" => "<EMAIL>",
              "document1" => "***********",
              "telephone" => {
                "ddd" => "31",
                "number" => "975577723"
              }
            }
          }
        }.to_json, success?: true)
      end

      before do
        allow(Betalabs::Api).to receive(:new).and_return(double("betalabs_api", subscription: subscription_response_data))
      end

      it "must call Webhook::Betalabs::Events::InactiveSubscription" do
        allow(Webhook::Betalabs::Events::InactiveSubscription).to receive(:new).and_return(double("action", action: nil))
        handler.parse(business:, request:)
        expect(Webhook::Betalabs::Events::InactiveSubscription).to have_received(:new)
      end
    end
  end
end
