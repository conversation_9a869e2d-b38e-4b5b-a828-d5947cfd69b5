require "rails_helper"

RSpec.describe Webhook::Betalabs::Events::ActiveSubscription, :vcr do
  let(:params) do
    {
      id: 2
    }
  end
  let(:business) { create(:business) }
  let!(:betalabs_client) { create(:betalabs_client, business:, url_subdomain: "clubesensa", token: "any_token") }
  let(:mocked_customer_cpf) { "***********" }
  let(:mocked_plan_id) { 1 }
  let(:subscription_response_data) do
    {
      "data" => {
        "id" => 2,
        "plan_id" => mocked_plan_id,
        "status" => "active",
        "consumer" => {
          "id" => 5,
          "full_name" => "<PERSON>",
          "email" => "<EMAIL>",
          "document1" => mocked_customer_cpf,
          "telephone" => {
            "ddd" => "31",
            "number" => "975577723"
          }
        }
      }.with_indifferent_access
    }
  end

  describe "#action" do
    it "must call create_authorized_user Action for business" do
      allow(Webhook::Actions::CreateAuthorizedUser).to receive(:new).and_call_original
      described_class.new(business:, params:, subscription: subscription_response_data).action.process

      expect(Webhook::Actions::CreateAuthorizedUser).to have_received(:new).with(
        business:,
        cpf: subscription_response_data.dig("data", "consumer", "document1"),
        name: subscription_response_data.dig("data", "consumer", "full_name"),
        email: subscription_response_data.dig("data", "consumer", "email"),
        phone: subscription_response_data.dig("data", "consumer", "telephone", "ddd") + subscription_response_data.dig("data", "consumer", "telephone", "number")
      )
      authorized_user = AuthorizedUser.last
      expect(authorized_user).to be_present
      expect(authorized_user.business).to eq(business)
      expect(authorized_user.cpf).to eq(subscription_response_data.dig("data", "consumer", "document1"))
    end

    context "when betalabs client has a mapping with business id for this plan" do
      context "when authorized already exists" do
        let!(:authorized_user) { create(:authorized_user, business:, cpf: mocked_customer_cpf) }
        let(:authorized_user_group) { create(:authorized_user_group, business:) }
        let(:target_subbusiness) { create(:business, main_business: business) }

        before do
          betalabs_client.update(plan_to_businesses_ids: {mocked_plan_id.to_s => target_subbusiness.id})
          authorized_user.update(authorized_user_group:)
        end

        it "must call change_authorized_user_business Action for target business" do
          allow(Webhook::Actions::ChangeAuthorizedUserBusiness).to receive(:new).and_call_original
          described_class.new(business:, params:, subscription: subscription_response_data).action.process

          expect(Webhook::Actions::ChangeAuthorizedUserBusiness).to have_received(:new)
          authorized_user.reload
          expect(authorized_user.cpf).to eq(subscription_response_data.dig("data", "consumer", "document1"))
          expect(authorized_user.business_id).to eq(target_subbusiness.id)
          expect(authorized_user.authorized_user_group.slug).to eq(authorized_user_group.slug)
          expect(authorized_user.authorized_user_group.id).not_to eq(authorized_user_group.id)
          expect(authorized_user.authorized_user_group.business_id).to eq(target_subbusiness.id)
        end
      end

      context "when authorized user does not exist yet" do
        let(:target_subbusiness) { create(:business, main_business: business) }

        before do
          betalabs_client.update(plan_to_businesses_ids: {mocked_plan_id.to_s => target_subbusiness.id})
        end

        it "must call create_authorized_user Action for target business" do
          allow(Webhook::Actions::CreateAuthorizedUser).to receive(:new).and_call_original
          described_class.new(business:, params:, subscription: subscription_response_data).action.process

          expect(Webhook::Actions::CreateAuthorizedUser).to have_received(:new)
          authorized_user = AuthorizedUser.last
          expect(authorized_user).to be_present
          expect(authorized_user.business).to eq(target_subbusiness)
          expect(authorized_user.cpf).to eq(subscription_response_data.dig("data", "consumer", "document1"))
        end
      end
    end
  end
end
