require "rails_helper"

RSpec.describe Webhook::Betalabs::Events::InactiveSubscription do
  let(:params) do
    {
      id: 2
    }
  end
  let(:business) { create(:business) }
  let!(:betalabs_client) { create(:betalabs_client, business:, url_subdomain: "clubesensa", token: "any_token") }
  let(:mocked_customer_cpf) { "***********" }
  let(:subscription_response_data) do
    {
      "data" => {
        "id" => 2,
        "plan_id" => 1,
        "status" => "active",
        "consumer" => {
          "id" => 5,
          "full_name" => "Lucas Octavio <PERSON>",
          "email" => "<EMAIL>",
          "document1" => mocked_customer_cpf,
          "telephone" => {
            "ddd" => "31",
            "number" => "975577723"
          }
        }
      }.with_indifferent_access
    }
  end

  describe "#action" do
    before do
      allow(Webhook::Actions::InactivateAuthorizedUser).to receive(:new).and_call_original
      allow(Webhook::Actions::ChangeAuthorizedUserBusiness).to receive(:new).and_call_original
    end

    context "when authorized user exists" do
      let!(:authorized_user) { create(:authorized_user, business:, cpf: mocked_customer_cpf) }
      let!(:authorized_user_on_unrelated_business) { create(:authorized_user, cpf: mocked_customer_cpf) }

      it "must call inactivate_authorized_user Action for business" do
        described_class.new(business:, params:, subscription: subscription_response_data).action.process
        expect(Webhook::Actions::InactivateAuthorizedUser).to have_received(:new).with(business:, cpf: mocked_customer_cpf)
        expect(authorized_user.reload.active).to eq(false)
        expect(authorized_user_on_unrelated_business.reload.active).to eq(true)
      end

      context "when authorized user has any group and betalab client has a free plan business" do
        let(:authorized_user_group) { create(:authorized_user_group, business:) }
        let(:target_subbusiness) { create(:business, main_business: business) }

        before do
          authorized_user.update!(authorized_user_group:)
          betalabs_client.update(free_plan_business_id: target_subbusiness.id)
        end

        it "must call change_authorized_user_business Action" do
          described_class.new(business:, params:, subscription: subscription_response_data).action.process
          expect(Webhook::Actions::ChangeAuthorizedUserBusiness).to have_received(:new)
          expect(authorized_user.reload.business_id).to eq(target_subbusiness.id)
          expect(authorized_user.authorized_user_group.slug).to eq(authorized_user_group.slug)
          expect(authorized_user.authorized_user_group.id).not_to eq(authorized_user_group.id)
          expect(authorized_user.authorized_user_group.business_id).to eq(target_subbusiness.id)
        end
      end
    end

    context "when authorized user does not exist" do
      it "must not call inactivate_authorized_user Action for business" do
        action = described_class.new(business:, params:, subscription: subscription_response_data).action
        expect(action).to be_nil
        expect(Webhook::Actions::InactivateAuthorizedUser).not_to have_received(:new).with(business:, cpf: mocked_customer_cpf)
      end
    end
  end
end
